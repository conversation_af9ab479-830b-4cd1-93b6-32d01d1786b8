{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"], "prettier.requireConfig": true, "vetur.validation.template": false, "vetur.validation.script": false, "vetur.validation.style": false, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}
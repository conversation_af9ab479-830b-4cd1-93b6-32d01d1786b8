"""认证API模块"""
from flask import Blueprint, jsonify, request
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity

bp = Blueprint('auth', __name__, url_prefix='/auth')


@bp.route('/login', methods=['POST'])
def login():
    """用户登录"""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    # 这里应该验证用户名和密码
    # 暂时使用简单的验证
    if username == 'admin' and password == 'password':
        access_token = create_access_token(identity=username)
        return jsonify({
            'message': '登录成功',
            'access_token': access_token
        }), 200
    else:
        return jsonify({
            'message': '用户名或密码错误'
        }), 401


@bp.route('/profile', methods=['GET'])
@jwt_required()
def profile():
    """获取用户信息"""
    current_user = get_jwt_identity()
    return jsonify({
        'message': '获取用户信息成功',
        'data': {
            'username': current_user
        }
    }), 200
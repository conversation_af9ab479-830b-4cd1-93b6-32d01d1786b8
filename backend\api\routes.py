from flask import jsonify
from backend.api import bp


@bp.route('/', methods=['GET'])
def api_index():
    """API根路径"""
    return jsonify({
        'message': '欢迎使用自动化任务管理工具API',
        'version': 'v1.0'
    }), 200


@bp.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'message': 'Automation Task Manager API is running'
    }), 200
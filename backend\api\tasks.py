"""任务管理API模块"""
from flask import Blueprint, jsonify, request

bp = Blueprint('tasks', __name__, url_prefix='/tasks')


@bp.route('/', methods=['GET'], strict_slashes=False)
def get_tasks():
    """获取任务列表"""
    return jsonify({
        'message': '获取任务列表',
        'data': []
    }), 200


@bp.route('/<int:task_id>', methods=['GET'], strict_slashes=False)
def get_task(task_id):
    """获取单个任务"""
    return jsonify({
        'message': f'获取任务 {task_id}',
        'data': {}
    }), 200


@bp.route('/', methods=['POST'], strict_slashes=False)
def create_task():
    """创建任务"""
    data = request.get_json()
    return jsonify({
        'message': '创建任务成功',
        'data': data
    }), 201


@bp.route('/<int:task_id>', methods=['PUT'], strict_slashes=False)
def update_task(task_id):
    """更新任务"""
    data = request.get_json()
    return jsonify({
        'message': f'更新任务 {task_id} 成功',
        'data': data
    }), 200


@bp.route('/<int:task_id>', methods=['DELETE'], strict_slashes=False)
def delete_task(task_id):
    """删除任务"""
    return jsonify({
        'message': f'删除任务 {task_id} 成功'
    }), 200
import os
import sys
from pathlib import Path

# 将项目根目录添加到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from flask import Flask
from flask_cors import CORS
from flask_jwt_extended import JWTManager
from flask_restful import Api
from flask_apscheduler import APScheduler

# 创建日志目录
log_dir = Path('logs')
log_dir.mkdir(exist_ok=True)


def create_app(config_class=None):
    """应用工厂函数"""
    app = Flask(__name__)
    
    # 加载配置
    if config_class:
        app.config.from_object(config_class)
    else:
        app.config.from_object('backend.config.Config')
    
    # 确保日志目录存在
    log_file = app.config.get('LOG_FILE', 'logs/app.log')
    log_dir = Path(log_file).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 初始化扩展
    cors = CORS(
        app,
        origins=app.config['CORS_ORIGINS'],
        supports_credentials=app.config['CORS_SUPPORTS_CREDENTIALS'],
        methods=app.config['CORS_METHODS']
    )
    jwt = JWTManager(app)
    scheduler = APScheduler()
    api = Api(app, prefix='/api/v1')
    
    # 配置JWT错误处理
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return {'message': 'Token已过期'}, 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return {'message': '无效的Token'}, 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        return {'message': '缺少Token'}, 401
    
    # 初始化调度器
    scheduler.init_app(app)
    # 启动调度器
    if not hasattr(scheduler, 'state') or scheduler.state == 0:  # STATE_STOPPED
        scheduler.start()
    
    # 注册日志系统
    from backend.utils.logger import LoggingMiddleware
    LoggingMiddleware(app)
    
    # 注册安全头中间件
    from backend.utils.middleware import security_headers_middleware
    security_headers_middleware(app)
    
    # 注册蓝图
    from backend.api import bp as api_bp
    app.register_blueprint(api_bp, url_prefix='/api/v1')
    
    # 注册错误处理
    from backend.utils.exceptions import register_error_handlers
    register_error_handlers(app)
    
    return app


def create_app_with_db():
    """创建应用并初始化数据库"""
    app = create_app()
    
    # 初始化数据库
    from backend.models import db
    db.init_app(app)
    
    # 创建数据库表
    with app.app_context():
        db.create_all()
    
    return app


if __name__ == '__main__':
    # 根据环境变量决定是否初始化数据库
    if os.environ.get('FLASK_INIT_DB') == 'true':
        app = create_app_with_db()
    else:
        app = create_app()
    
    app.run(
        host=os.environ.get('FLASK_HOST') or '127.0.0.1',
        port=os.environ.get('FLASK_PORT') or 5000,
        debug=app.config['DEBUG']
    )
import os


class Config:
    """应用基础配置类"""
    
    # Flask基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.dirname(__file__), '..', 'data', 'app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }
    
    # APScheduler配置
    SCHEDULER_API_ENABLED = True
    SCHEDULER_TIMEZONE = 'Asia/Shanghai'
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.environ.get('LOG_FILE') or 'logs/app.log'
    
    # 脚本执行配置
    SCRIPTS_DIR = os.environ.get('SCRIPTS_DIR') or os.path.join(os.path.dirname(__file__), '..', 'scripts')
    
    # API配置
    API_TITLE = "自动化任务管理工具API"
    API_VERSION = "v1"
    OPENAPI_VERSION = "3.0.2"
    
    # 安全配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-string'
    JWT_ACCESS_TOKEN_EXPIRES = 3600  # 1小时
    JWT_REFRESH_TOKEN_EXPIRES = 86400  # 24小时
    JWT_TOKEN_LOCATION = ['headers']
    JWT_HEADER_NAME = 'Authorization'
    JWT_HEADER_TYPE = 'Bearer'
    
    # 跨域配置
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS') or '*'
    CORS_SUPPORTS_CREDENTIALS = True
    CORS_METHODS = ['GET', 'HEAD', 'POST', 'OPTIONS', 'PUT', 'PATCH', 'DELETE']
    
    # RESTful配置
    RESTFUL_JSON = {
        'ensure_ascii': False,
        'indent': 2
    }
    
    # 应用行为配置
    DEBUG = os.environ.get('FLASK_ENV') == 'development'
    TESTING = False
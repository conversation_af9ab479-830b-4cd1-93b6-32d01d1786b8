"""API响应格式化模块"""
from flask import jsonify
from backend.utils.http_status import HttpStatus


def success(data=None, message="操作成功", status_code=HttpStatus.OK):
    """成功响应"""
    response = {
        'success': True,
        'message': message,
        'data': data
    }
    return jsonify(response), status_code


def error(message="操作失败", error_code="ERROR", status_code=HttpStatus.INTERNAL_SERVER_ERROR, data=None):
    """错误响应"""
    response = {
        'success': False,
        'message': message,
        'error_code': error_code,
        'data': data
    }
    return jsonify(response), status_code


def paginated_response(data, total, page, per_page, message="获取成功"):
    """分页响应"""
    response = {
        'success': True,
        'message': message,
        'data': data,
        'pagination': {
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        }
    }
    return jsonify(response), HttpStatus.OK
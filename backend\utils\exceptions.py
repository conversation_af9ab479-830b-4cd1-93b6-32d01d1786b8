"""自定义异常和错误处理"""
from flask import jsonify
from flask_jwt_extended import J<PERSON><PERSON>ana<PERSON>
from backend.utils.http_status import HttpStatus


class APIException(Exception):
    """API基础异常类"""
    status_code = HttpStatus.INTERNAL_SERVER_ERROR
    message = '服务器内部错误'
    error_code = 'INTERNAL_ERROR'

    def __init__(self, message=None, status_code=None, error_code=None):
        if message:
            self.message = message
        if status_code:
            self.status_code = status_code
        if error_code:
            self.error_code = error_code
        super().__init__(self.message)

    def to_dict(self):
        """将异常转换为字典格式"""
        return {
            'error': self.error_code,
            'message': self.message,
            'status_code': self.status_code
        }


class TaskNotFoundException(APIException):
    """任务未找到异常"""
    status_code = HttpStatus.NOT_FOUND
    message = '任务未找到'
    error_code = 'TASK_NOT_FOUND'


class ScriptNotFoundException(APIException):
    """脚本未找到异常"""
    status_code = HttpStatus.NOT_FOUND
    message = '脚本未找到'
    error_code = 'SCRIPT_NOT_FOUND'


class ValidationError(APIException):
    """验证错误异常"""
    status_code = HttpStatus.BAD_REQUEST
    message = '请求参数验证失败'
    error_code = 'VALIDATION_ERROR'


class AuthenticationError(APIException):
    """认证错误异常"""
    status_code = HttpStatus.UNAUTHORIZED
    message = '认证失败'
    error_code = 'AUTHENTICATION_ERROR'


class AuthorizationError(APIException):
    """授权错误异常"""
    status_code = HttpStatus.FORBIDDEN
    message = '权限不足'
    error_code = 'AUTHORIZATION_ERROR'


def register_error_handlers(app):
    """注册全局错误处理"""
    @app.errorhandler(APIException)
    def handle_api_exception(error):
        response = error.to_dict()
        return jsonify(response), error.status_code

    @app.errorhandler(HttpStatus.NOT_FOUND)
    def handle_not_found(error):
        response = {
            'error': 'NOT_FOUND',
            'message': '请求的资源未找到',
            'status_code': HttpStatus.NOT_FOUND
        }
        return jsonify(response), HttpStatus.NOT_FOUND

    @app.errorhandler(HttpStatus.INTERNAL_SERVER_ERROR)
    def handle_internal_error(error):
        response = {
            'error': 'INTERNAL_SERVER_ERROR',
            'message': '服务器内部错误',
            'status_code': HttpStatus.INTERNAL_SERVER_ERROR
        }
        return jsonify(response), HttpStatus.INTERNAL_SERVER_ERROR
"""日志配置模块"""
import os
import sys
from pathlib import Path
from loguru import logger


def setup_logger(log_level="INFO", log_file="logs/app.log"):
    """配置loguru日志"""
    # 创建日志目录
    log_dir = Path(log_file).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 移除默认的日志处理器
    logger.remove()
    
    # 添加控制台输出
    logger.add(
        sys.stdout,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )
    
    # 添加文件输出
    logger.add(
        log_file,
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="500 MB",
        retention="30 days",
        compression="zip",
        encoding="utf-8"
    )
    
    return logger


class LoggingMiddleware:
    """日志中间件"""
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化日志中间件"""
        # 获取日志配置
        log_level = app.config.get('LOG_LEVEL', 'INFO')
        log_file = app.config.get('LOG_FILE', 'logs/app.log')
        
        # 设置日志
        setup_logger(log_level, log_file)
        
        # 注册请求日志处理器
        app.before_request(self.before_request)
        app.after_request(self.after_request)
        app.teardown_request(self.teardown_request)
    
    def before_request(self):
        """请求前处理"""
        from flask import request, g
        import uuid
        import time
        
        g.request_id = str(uuid.uuid4())
        g.start_time = time.time()
        logger.info(f"[{g.request_id}] {request.method} {request.url}")
    
    def after_request(self, response):
        """请求后处理"""
        from flask import g
        import time
        
        if hasattr(g, 'start_time') and hasattr(g, 'request_id'):
            duration = time.time() - g.start_time
            logger.info(f"[{g.request_id}] Response: {response.status_code} Duration: {duration:.4f}s")
        
        return response
    
    def teardown_request(self, exception):
        """请求结束处理"""
        from flask import g
        
        if exception:
            if hasattr(g, 'request_id'):
                logger.error(f"[{g.request_id}] Request failed with exception: {exception}")
"""自定义中间件"""
from flask import request, g
import time
import uuid


def request_id_middleware(app):
    """请求ID中间件，为每个请求生成唯一ID"""
    @app.before_request
    def before_request():
        g.request_id = str(uuid.uuid4())
        g.start_time = time.time()

    @app.after_request
    def after_request(response):
        if hasattr(g, 'start_time'):
            duration = time.time() - g.start_time
            response.headers['X-Response-Time'] = f'{duration:.6f}s'
        if hasattr(g, 'request_id'):
            response.headers['X-Request-ID'] = g.request_id
        return response


def security_headers_middleware(app):
    """安全头中间件，添加安全相关的HTTP头"""
    @app.after_request
    def after_request(response):
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        return response


def logging_middleware(app):
    """请求日志中间件"""
    @app.before_request
    def before_request():
        if hasattr(g, 'request_id'):
            app.logger.info(f'Request [{g.request_id}]: {request.method} {request.url}')

    @app.after_request
    def after_request(response):
        if hasattr(g, 'request_id'):
            app.logger.info(f'Response [{g.request_id}]: {response.status_code}')
        return response
2025-07-30 13:14:30.839 | INFO     | backend.utils.logger:before_request:68 - [02fbe116-7fba-43c4-9ec5-b5420de864d9] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ih79fjy9
2025-07-30 13:14:30.849 | ERROR    | backend.utils.logger:teardown_request:86 - [02fbe116-7fba-43c4-9ec5-b5420de864d9] Request failed with exception: name 'time' is not defined
2025-07-30 13:14:31.097 | INFO     | backend.utils.logger:before_request:68 - [502d5bf8-c7ad-4db8-9e78-98420f2263be] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ih7j1h05
2025-07-30 13:14:31.101 | ERROR    | backend.utils.logger:teardown_request:86 - [502d5bf8-c7ad-4db8-9e78-98420f2263be] Request failed with exception: name 'time' is not defined
2025-07-30 13:14:31.189 | INFO     | backend.utils.logger:before_request:68 - [8ebc5313-a362-45af-b14d-28b7d6141f7e] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ih7pi0cy
2025-07-30 13:14:31.189 | ERROR    | backend.utils.logger:teardown_request:86 - [8ebc5313-a362-45af-b14d-28b7d6141f7e] Request failed with exception: name 'time' is not defined
2025-07-30 13:14:31.420 | INFO     | backend.utils.logger:before_request:68 - [79a41579-1898-4cea-8c14-2f6589242bc6] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ih7rzflk
2025-07-30 13:14:31.420 | ERROR    | backend.utils.logger:teardown_request:86 - [79a41579-1898-4cea-8c14-2f6589242bc6] Request failed with exception: name 'time' is not defined
2025-07-30 13:14:31.510 | INFO     | backend.utils.logger:before_request:68 - [3f34c248-f014-41e5-8580-6d59cf87ebf6] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ih7yfxdf
2025-07-30 13:14:31.510 | ERROR    | backend.utils.logger:teardown_request:86 - [3f34c248-f014-41e5-8580-6d59cf87ebf6] Request failed with exception: name 'time' is not defined
2025-07-30 13:14:36.529 | INFO     | backend.utils.logger:before_request:68 - [e9a8e2d2-4b95-4b2b-bde0-5c96dc67e36d] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ihbw08be
2025-07-30 13:14:36.533 | ERROR    | backend.utils.logger:teardown_request:86 - [e9a8e2d2-4b95-4b2b-bde0-5c96dc67e36d] Request failed with exception: name 'time' is not defined
2025-07-30 13:14:42.846 | INFO     | backend.utils.logger:before_request:68 - [f68648f0-c293-4155-94f7-70aac05ce6fe] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ihgitge2
2025-07-30 13:14:42.854 | ERROR    | backend.utils.logger:teardown_request:86 - [f68648f0-c293-4155-94f7-70aac05ce6fe] Request failed with exception: name 'time' is not defined
2025-07-30 13:14:43.103 | INFO     | backend.utils.logger:before_request:68 - [beb60ae0-ca31-418b-9cc5-2507d52260cc] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ihgryqht
2025-07-30 13:14:43.107 | ERROR    | backend.utils.logger:teardown_request:86 - [beb60ae0-ca31-418b-9cc5-2507d52260cc] Request failed with exception: name 'time' is not defined
2025-07-30 13:14:43.170 | INFO     | backend.utils.logger:before_request:68 - [67c55ca7-c69c-466e-8874-72b10218390a] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ihgywnel
2025-07-30 13:14:43.176 | ERROR    | backend.utils.logger:teardown_request:86 - [67c55ca7-c69c-466e-8874-72b10218390a] Request failed with exception: name 'time' is not defined
2025-07-30 13:14:43.419 | INFO     | backend.utils.logger:before_request:68 - [07c7fc0c-c566-4c20-9b11-2d67a36c48f6] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ihh0r6n8
2025-07-30 13:14:43.423 | ERROR    | backend.utils.logger:teardown_request:86 - [07c7fc0c-c566-4c20-9b11-2d67a36c48f6] Request failed with exception: name 'time' is not defined
2025-07-30 13:14:48.539 | INFO     | backend.utils.logger:before_request:68 - [fc5f6487-876c-4705-a194-d1850844f8c0] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ihl5mi57
2025-07-30 13:14:48.542 | ERROR    | backend.utils.logger:teardown_request:86 - [fc5f6487-876c-4705-a194-d1850844f8c0] Request failed with exception: name 'time' is not defined
2025-07-30 13:14:54.847 | INFO     | backend.utils.logger:before_request:68 - [12b42906-fec7-48ff-8b01-cb1d3be04e93] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ihps9yic
2025-07-30 13:14:54.850 | ERROR    | backend.utils.logger:teardown_request:86 - [12b42906-fec7-48ff-8b01-cb1d3be04e93] Request failed with exception: name 'time' is not defined
2025-07-30 13:14:55.107 | INFO     | backend.utils.logger:before_request:68 - [70d817a8-9b82-4360-af6a-f1450b5f04af] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ihq11vye
2025-07-30 13:14:55.111 | ERROR    | backend.utils.logger:teardown_request:86 - [70d817a8-9b82-4360-af6a-f1450b5f04af] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:00.534 | INFO     | backend.utils.logger:before_request:68 - [ca92b6b4-5ad6-4f91-81fa-562aa0945fa2] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ihuerddo
2025-07-30 13:15:00.537 | ERROR    | backend.utils.logger:teardown_request:86 - [ca92b6b4-5ad6-4f91-81fa-562aa0945fa2] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:06.839 | INFO     | backend.utils.logger:before_request:68 - [77c2c9fa-e760-4471-850d-cc45074815bc] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ihz1jzps
2025-07-30 13:15:06.843 | ERROR    | backend.utils.logger:teardown_request:86 - [77c2c9fa-e760-4471-850d-cc45074815bc] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:07.105 | INFO     | backend.utils.logger:before_request:68 - [9f583ef6-61fe-42d0-b3b6-e07fed9a958c] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ihza9alv
2025-07-30 13:15:07.108 | ERROR    | backend.utils.logger:teardown_request:86 - [9f583ef6-61fe-42d0-b3b6-e07fed9a958c] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:12.526 | INFO     | backend.utils.logger:before_request:68 - [ce4584bb-d3e6-4707-b1df-5aabfa2e16e0] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ii3ny2a5
2025-07-30 13:15:12.532 | ERROR    | backend.utils.logger:teardown_request:86 - [ce4584bb-d3e6-4707-b1df-5aabfa2e16e0] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:18.843 | INFO     | backend.utils.logger:before_request:68 - [e3889d1e-b588-4488-9ab6-def16e14b951] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ii8as4s8
2025-07-30 13:15:18.847 | ERROR    | backend.utils.logger:teardown_request:86 - [e3889d1e-b588-4488-9ab6-def16e14b951] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:21.532 | INFO     | backend.utils.logger:before_request:68 - [e053bd8c-dd01-4184-a089-e77f7c2fff8d] GET http://localhost:5000/api/statistics/overview
2025-07-30 13:15:21.536 | ERROR    | backend.utils.logger:teardown_request:86 - [e053bd8c-dd01-4184-a089-e77f7c2fff8d] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:21.836 | INFO     | backend.utils.logger:before_request:68 - [365c5529-9813-4fc4-bd37-68ee2014a658] GET http://localhost:5000/api/statistics/execution-charts
2025-07-30 13:15:21.836 | INFO     | backend.utils.logger:before_request:68 - [d619fad0-c070-47d8-bd84-8ef38a591031] GET http://localhost:5000/api/statistics/recent-executions
2025-07-30 13:15:21.842 | ERROR    | backend.utils.logger:teardown_request:86 - [365c5529-9813-4fc4-bd37-68ee2014a658] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:21.848 | ERROR    | backend.utils.logger:teardown_request:86 - [d619fad0-c070-47d8-bd84-8ef38a591031] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:24.845 | INFO     | backend.utils.logger:before_request:68 - [d7cb1607-c4f7-4ea0-9a66-2bc31a28f4c5] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iicxc4y1
2025-07-30 13:15:24.849 | ERROR    | backend.utils.logger:teardown_request:86 - [d7cb1607-c4f7-4ea0-9a66-2bc31a28f4c5] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:25.092 | INFO     | backend.utils.logger:before_request:68 - [59c144d6-a54c-4c21-90e2-0124bcaffaff] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iid6f917
2025-07-30 13:15:25.096 | ERROR    | backend.utils.logger:teardown_request:86 - [59c144d6-a54c-4c21-90e2-0124bcaffaff] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:30.530 | INFO     | backend.utils.logger:before_request:68 - [a8e8dc52-8100-474e-ad85-83e12caaf4d3] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iihk09zy
2025-07-30 13:15:30.530 | ERROR    | backend.utils.logger:teardown_request:86 - [a8e8dc52-8100-474e-ad85-83e12caaf4d3] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:36.849 | INFO     | backend.utils.logger:before_request:68 - [13585f49-1a6d-4f09-a220-79c8d66bcf42] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iim6vy0m
2025-07-30 13:15:36.851 | ERROR    | backend.utils.logger:teardown_request:86 - [13585f49-1a6d-4f09-a220-79c8d66bcf42] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:42.538 | INFO     | backend.utils.logger:before_request:68 - [94b731c0-d36e-414a-a219-97763bff20de] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iiqtkd6x
2025-07-30 13:15:42.540 | ERROR    | backend.utils.logger:teardown_request:86 - [94b731c0-d36e-414a-a219-97763bff20de] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:42.852 | INFO     | backend.utils.logger:before_request:68 - [41c2584e-52ef-4a21-9213-e81b01941b1d] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iiqtss6x
2025-07-30 13:15:42.855 | ERROR    | backend.utils.logger:teardown_request:86 - [41c2584e-52ef-4a21-9213-e81b01941b1d] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:48.526 | INFO     | backend.utils.logger:before_request:68 - [fede01fc-0f65-4762-89c9-4c9d5933372d] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iivfxfot
2025-07-30 13:15:48.530 | ERROR    | backend.utils.logger:teardown_request:86 - [fede01fc-0f65-4762-89c9-4c9d5933372d] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:54.841 | INFO     | backend.utils.logger:before_request:68 - [d9b56bff-ffe5-4533-b86a-75f174a71de1] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ij02rdaa
2025-07-30 13:15:54.845 | ERROR    | backend.utils.logger:teardown_request:86 - [d9b56bff-ffe5-4533-b86a-75f174a71de1] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:55.105 | INFO     | backend.utils.logger:before_request:68 - [d1c545b9-ab57-4a7f-a2bf-891d3bf80a86] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ij0bnzi4
2025-07-30 13:15:55.107 | ERROR    | backend.utils.logger:teardown_request:86 - [d1c545b9-ab57-4a7f-a2bf-891d3bf80a86] Request failed with exception: name 'time' is not defined
2025-07-30 13:15:55.153 | INFO     | backend.utils.logger:before_request:68 - [ffd10d81-8f9f-4932-8821-5b561d3a6bf0] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ij0ivu79
2025-07-30 13:15:55.156 | ERROR    | backend.utils.logger:teardown_request:86 - [ffd10d81-8f9f-4932-8821-5b561d3a6bf0] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:00.532 | INFO     | backend.utils.logger:before_request:68 - [e8e1856d-9ca5-4ce5-9135-58c7b3626555] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ij4pcv01
2025-07-30 13:16:00.537 | ERROR    | backend.utils.logger:teardown_request:86 - [e8e1856d-9ca5-4ce5-9135-58c7b3626555] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:06.844 | INFO     | backend.utils.logger:before_request:68 - [f27598be-ad0d-4586-af2b-c49f94167f92] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ij9c2gl3
2025-07-30 13:16:06.849 | ERROR    | backend.utils.logger:teardown_request:86 - [f27598be-ad0d-4586-af2b-c49f94167f92] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:07.099 | INFO     | backend.utils.logger:before_request:68 - [15302903-fba2-47fb-864e-ca3eba11dfbf] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ij9l1xig
2025-07-30 13:16:07.103 | ERROR    | backend.utils.logger:teardown_request:86 - [15302903-fba2-47fb-864e-ca3eba11dfbf] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:07.166 | INFO     | backend.utils.logger:before_request:68 - [0ac03730-1843-4f6a-98b9-05199bcf63f4] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ij9s2gh5
2025-07-30 13:16:07.170 | ERROR    | backend.utils.logger:teardown_request:86 - [0ac03730-1843-4f6a-98b9-05199bcf63f4] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:12.535 | INFO     | backend.utils.logger:before_request:68 - [492ea8dd-542d-43b9-8cf2-f42768a0648c] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ijdyrduy
2025-07-30 13:16:12.539 | ERROR    | backend.utils.logger:teardown_request:86 - [492ea8dd-542d-43b9-8cf2-f42768a0648c] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:12.853 | INFO     | backend.utils.logger:before_request:68 - [9219d398-6b45-4f4f-a139-3c1c3abd5b43] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ijdz38p3
2025-07-30 13:16:12.857 | ERROR    | backend.utils.logger:teardown_request:86 - [9219d398-6b45-4f4f-a139-3c1c3abd5b43] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:13.116 | INFO     | backend.utils.logger:before_request:68 - [0c40f3de-6e3e-462a-a833-b771cccd4dac] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ije7yqnc
2025-07-30 13:16:13.120 | ERROR    | backend.utils.logger:teardown_request:86 - [0c40f3de-6e3e-462a-a833-b771cccd4dac] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:13.171 | INFO     | backend.utils.logger:before_request:68 - [177a8fc1-478c-463c-ba87-bd256a57173a] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ijef78jq
2025-07-30 13:16:13.175 | ERROR    | backend.utils.logger:teardown_request:86 - [177a8fc1-478c-463c-ba87-bd256a57173a] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:13.440 | INFO     | backend.utils.logger:before_request:68 - [9d3e4a8c-2700-4424-b66e-1cfa61a46029] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ijegr1xy
2025-07-30 13:16:13.442 | ERROR    | backend.utils.logger:teardown_request:86 - [9d3e4a8c-2700-4424-b66e-1cfa61a46029] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:18.538 | INFO     | backend.utils.logger:before_request:68 - [b6fcb3e5-90fb-42f7-8dcd-4139935e79f5] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ijilk7zj
2025-07-30 13:16:18.543 | ERROR    | backend.utils.logger:teardown_request:86 - [b6fcb3e5-90fb-42f7-8dcd-4139935e79f5] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:21.840 | INFO     | backend.utils.logger:before_request:68 - [ac06f290-39ba-49b6-afd1-5938867e6f77] GET http://localhost:5000/api/statistics/overview
2025-07-30 13:16:21.840 | INFO     | backend.utils.logger:before_request:68 - [d2b728f7-5c37-4b2a-902f-e50784604581] GET http://localhost:5000/api/statistics/execution-charts
2025-07-30 13:16:21.845 | INFO     | backend.utils.logger:before_request:68 - [d346c7ff-465b-475c-ab77-c069040fbc91] GET http://localhost:5000/api/statistics/recent-executions
2025-07-30 13:16:21.849 | ERROR    | backend.utils.logger:teardown_request:86 - [ac06f290-39ba-49b6-afd1-5938867e6f77] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:21.855 | ERROR    | backend.utils.logger:teardown_request:86 - [d2b728f7-5c37-4b2a-902f-e50784604581] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:21.857 | ERROR    | backend.utils.logger:teardown_request:86 - [d346c7ff-465b-475c-ab77-c069040fbc91] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:24.536 | INFO     | backend.utils.logger:before_request:68 - [f368b62b-7ba5-4e19-8fe3-4c75030cd0ff] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ijn890wc
2025-07-30 13:16:24.540 | ERROR    | backend.utils.logger:teardown_request:86 - [f368b62b-7ba5-4e19-8fe3-4c75030cd0ff] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:24.851 | INFO     | backend.utils.logger:before_request:68 - [9cf9df1c-5988-4d11-92f0-e78c9559a0e4] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ijn8ggzd
2025-07-30 13:16:24.856 | ERROR    | backend.utils.logger:teardown_request:86 - [9cf9df1c-5988-4d11-92f0-e78c9559a0e4] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:30.536 | INFO     | backend.utils.logger:before_request:68 - [332ae8e6-3781-4f35-a95b-72cce6187cac] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ijruwlqj
2025-07-30 13:16:30.540 | ERROR    | backend.utils.logger:teardown_request:86 - [332ae8e6-3781-4f35-a95b-72cce6187cac] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:30.851 | INFO     | backend.utils.logger:before_request:68 - [15698c79-1d41-497a-ae2c-48a25f10bc63] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ijrv4mto
2025-07-30 13:16:30.855 | ERROR    | backend.utils.logger:teardown_request:86 - [15698c79-1d41-497a-ae2c-48a25f10bc63] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:36.530 | INFO     | backend.utils.logger:before_request:68 - [dd258d9b-ade8-4329-a0f2-cc38d03f70e4] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ijwha4cz
2025-07-30 13:16:36.534 | ERROR    | backend.utils.logger:teardown_request:86 - [dd258d9b-ade8-4329-a0f2-cc38d03f70e4] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:42.834 | INFO     | backend.utils.logger:before_request:68 - [4a02debb-37ea-4702-867a-08b5b26cb4c9] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ik140ay3
2025-07-30 13:16:42.837 | ERROR    | backend.utils.logger:teardown_request:86 - [4a02debb-37ea-4702-867a-08b5b26cb4c9] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:43.099 | INFO     | backend.utils.logger:before_request:68 - [01b36766-8552-4459-a18f-925a4687874a] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ik1cpn6b
2025-07-30 13:16:43.103 | ERROR    | backend.utils.logger:teardown_request:86 - [01b36766-8552-4459-a18f-925a4687874a] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:48.532 | INFO     | backend.utils.logger:before_request:68 - [02255669-589b-437a-83ce-344281274264] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ik5qqqvx
2025-07-30 13:16:48.532 | ERROR    | backend.utils.logger:teardown_request:86 - [02255669-589b-437a-83ce-344281274264] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:48.846 | INFO     | backend.utils.logger:before_request:68 - [8707ceca-2ac9-4aaf-8a80-faf4f95d40a0] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ik5qz0be
2025-07-30 13:16:48.851 | ERROR    | backend.utils.logger:teardown_request:86 - [8707ceca-2ac9-4aaf-8a80-faf4f95d40a0] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:49.103 | INFO     | backend.utils.logger:before_request:68 - [6cfd645a-e210-4292-b898-3ac6d420e481] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ik5zp15t
2025-07-30 13:16:49.103 | ERROR    | backend.utils.logger:teardown_request:86 - [6cfd645a-e210-4292-b898-3ac6d420e481] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:49.154 | INFO     | backend.utils.logger:before_request:68 - [6bcd451f-f655-4c3c-8c7a-6b3a83b557bd] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ik66tf4h
2025-07-30 13:16:49.154 | ERROR    | backend.utils.logger:teardown_request:86 - [6bcd451f-f655-4c3c-8c7a-6b3a83b557bd] Request failed with exception: name 'time' is not defined
2025-07-30 13:16:54.543 | INFO     | backend.utils.logger:before_request:68 - [f42d336d-cf1e-46eb-8261-a874b72c39f4] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ikadgiw9
2025-07-30 13:16:54.543 | ERROR    | backend.utils.logger:teardown_request:86 - [f42d336d-cf1e-46eb-8261-a874b72c39f4] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:00.851 | INFO     | backend.utils.logger:before_request:68 - [98573d98-087d-40ac-8a27-a6aab9b1e543] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ikf04lra
2025-07-30 13:17:00.855 | ERROR    | backend.utils.logger:teardown_request:86 - [98573d98-087d-40ac-8a27-a6aab9b1e543] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:06.539 | INFO     | backend.utils.logger:before_request:68 - [673b2406-cae3-4124-af5c-798cc940ea0d] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ikjmxdqh
2025-07-30 13:17:06.543 | ERROR    | backend.utils.logger:teardown_request:86 - [673b2406-cae3-4124-af5c-798cc940ea0d] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:06.852 | INFO     | backend.utils.logger:before_request:68 - [97583563-5764-4a4c-aab2-5e861d523bce] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ikjn90sk
2025-07-30 13:17:06.857 | ERROR    | backend.utils.logger:teardown_request:86 - [97583563-5764-4a4c-aab2-5e861d523bce] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:07.115 | INFO     | backend.utils.logger:before_request:68 - [711fb07e-a98d-41f3-9803-06b40a64fb7c] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ikjvzyhe
2025-07-30 13:17:07.119 | ERROR    | backend.utils.logger:teardown_request:86 - [711fb07e-a98d-41f3-9803-06b40a64fb7c] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:12.535 | INFO     | backend.utils.logger:before_request:68 - [1f9130cf-974c-4ee2-8955-64c4493eea53] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iko9fk4p
2025-07-30 13:17:12.539 | ERROR    | backend.utils.logger:teardown_request:86 - [1f9130cf-974c-4ee2-8955-64c4493eea53] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:18.848 | INFO     | backend.utils.logger:before_request:68 - [924ab8be-cd09-4b70-8524-dc698d6025ad] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ikswatbv
2025-07-30 13:17:18.853 | ERROR    | backend.utils.logger:teardown_request:86 - [924ab8be-cd09-4b70-8524-dc698d6025ad] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:19.109 | INFO     | backend.utils.logger:before_request:68 - [fadb000b-9600-4d02-a530-ca6edc743252] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ikt54e0f
2025-07-30 13:17:19.113 | ERROR    | backend.utils.logger:teardown_request:86 - [fadb000b-9600-4d02-a530-ca6edc743252] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:21.532 | INFO     | backend.utils.logger:before_request:68 - [83a21ac8-dbb3-43a5-ba19-4b3ffd50bea9] GET http://localhost:5000/api/statistics/overview
2025-07-30 13:17:21.535 | ERROR    | backend.utils.logger:teardown_request:86 - [83a21ac8-dbb3-43a5-ba19-4b3ffd50bea9] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:21.834 | INFO     | backend.utils.logger:before_request:68 - [5ccbc0a0-9307-41e1-9cb2-1cf8ff748745] GET http://localhost:5000/api/statistics/execution-charts
2025-07-30 13:17:21.836 | INFO     | backend.utils.logger:before_request:68 - [f8b6bca6-05b1-4e9b-a431-c073686102aa] GET http://localhost:5000/api/statistics/recent-executions
2025-07-30 13:17:21.840 | ERROR    | backend.utils.logger:teardown_request:86 - [5ccbc0a0-9307-41e1-9cb2-1cf8ff748745] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:21.841 | ERROR    | backend.utils.logger:teardown_request:86 - [f8b6bca6-05b1-4e9b-a431-c073686102aa] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:24.843 | INFO     | backend.utils.logger:before_request:68 - [74bb2460-3ffd-4a52-aa89-266946cbc27f] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ikxisrfh
2025-07-30 13:17:24.847 | ERROR    | backend.utils.logger:teardown_request:86 - [74bb2460-3ffd-4a52-aa89-266946cbc27f] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:25.103 | INFO     | backend.utils.logger:before_request:68 - [5cd39f56-73b4-4829-b471-18361cc4a30d] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ikxrmhxg
2025-07-30 13:17:25.108 | ERROR    | backend.utils.logger:teardown_request:86 - [5cd39f56-73b4-4829-b471-18361cc4a30d] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:30.525 | INFO     | backend.utils.logger:before_request:68 - [72f84860-a950-4d71-9769-423fa6bdb8ce] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=il2595fm
2025-07-30 13:17:30.529 | ERROR    | backend.utils.logger:teardown_request:86 - [72f84860-a950-4d71-9769-423fa6bdb8ce] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:36.833 | INFO     | backend.utils.logger:before_request:68 - [1af7eda8-26e5-42c7-9d43-c657d2434c69] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=il6s16aq
2025-07-30 13:17:36.838 | ERROR    | backend.utils.logger:teardown_request:86 - [1af7eda8-26e5-42c7-9d43-c657d2434c69] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:37.095 | INFO     | backend.utils.logger:before_request:68 - [4adefc8a-d409-456e-a548-bae6a5e24505] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=il70opeo
2025-07-30 13:17:37.099 | ERROR    | backend.utils.logger:teardown_request:86 - [4adefc8a-d409-456e-a548-bae6a5e24505] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:37.155 | INFO     | backend.utils.logger:before_request:68 - [358e11f0-04c7-4043-9de4-04c2f8bb45c7] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=il77xw3i
2025-07-30 13:17:37.158 | ERROR    | backend.utils.logger:teardown_request:86 - [358e11f0-04c7-4043-9de4-04c2f8bb45c7] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:42.540 | INFO     | backend.utils.logger:before_request:68 - [274910ec-8a7a-414e-a069-18dc5b2dd5a0] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ilbexq17
2025-07-30 13:17:42.542 | ERROR    | backend.utils.logger:teardown_request:86 - [274910ec-8a7a-414e-a069-18dc5b2dd5a0] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:48.845 | INFO     | backend.utils.logger:before_request:68 - [98b30046-bad1-44e8-a87f-88934fa09568] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ilg1iv9n
2025-07-30 13:17:48.849 | ERROR    | backend.utils.logger:teardown_request:86 - [98b30046-bad1-44e8-a87f-88934fa09568] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:49.097 | INFO     | backend.utils.logger:before_request:68 - [c9e6a05f-1948-4146-b818-231fb7c9d5e5] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ilgadi9s
2025-07-30 13:17:49.099 | ERROR    | backend.utils.logger:teardown_request:86 - [c9e6a05f-1948-4146-b818-231fb7c9d5e5] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:49.159 | INFO     | backend.utils.logger:before_request:68 - [75139a82-f383-4768-8842-2c9e0643aecf] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ilghb6j0
2025-07-30 13:17:49.163 | ERROR    | backend.utils.logger:teardown_request:86 - [75139a82-f383-4768-8842-2c9e0643aecf] Request failed with exception: name 'time' is not defined
2025-07-30 13:17:54.849 | INFO     | backend.utils.logger:before_request:68 - [a40b3fbf-8d21-4b9a-97a9-49f6caa0c154] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ilko9tn1
2025-07-30 13:17:54.849 | INFO     | backend.utils.logger:after_request:77 - [a40b3fbf-8d21-4b9a-97a9-49f6caa0c154] Response: 404 Duration: 0.0000s
2025-07-30 13:18:00.526 | INFO     | backend.utils.logger:before_request:68 - [3a3c527e-97bf-4153-a2a0-68d3bcb6e55c] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ilpanmes
2025-07-30 13:18:00.526 | INFO     | backend.utils.logger:after_request:77 - [3a3c527e-97bf-4153-a2a0-68d3bcb6e55c] Response: 404 Duration: 0.0000s
2025-07-30 13:18:06.845 | INFO     | backend.utils.logger:before_request:68 - [528685b2-95ed-440f-8968-bd0908d5dcf6] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iltxgrex
2025-07-30 13:18:06.845 | INFO     | backend.utils.logger:after_request:77 - [528685b2-95ed-440f-8968-bd0908d5dcf6] Response: 404 Duration: 0.0000s
2025-07-30 13:18:07.098 | INFO     | backend.utils.logger:before_request:68 - [a9b71727-5a16-45ed-8e80-41dac4e09329] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ilu69a84
2025-07-30 13:18:07.100 | INFO     | backend.utils.logger:after_request:77 - [a9b71727-5a16-45ed-8e80-41dac4e09329] Response: 404 Duration: 0.0014s
2025-07-30 13:18:07.155 | INFO     | backend.utils.logger:before_request:68 - [15666b0b-1da9-4f67-8c6f-43bcc77cbf9f] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iluda2m4
2025-07-30 13:18:07.156 | INFO     | backend.utils.logger:after_request:77 - [15666b0b-1da9-4f67-8c6f-43bcc77cbf9f] Response: 404 Duration: 0.0010s
2025-07-30 13:18:12.538 | INFO     | backend.utils.logger:before_request:68 - [d36835ca-4833-429e-873a-0497e83bf9e7] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ilyk601y
2025-07-30 13:18:12.539 | INFO     | backend.utils.logger:after_request:77 - [d36835ca-4833-429e-873a-0497e83bf9e7] Response: 404 Duration: 0.0011s
2025-07-30 13:18:12.846 | INFO     | backend.utils.logger:before_request:68 - [a1982689-4ffd-422e-960d-8ae99a9e50f6] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ilykdvcd
2025-07-30 13:18:12.846 | INFO     | backend.utils.logger:after_request:77 - [a1982689-4ffd-422e-960d-8ae99a9e50f6] Response: 404 Duration: 0.0000s
2025-07-30 13:18:18.534 | INFO     | backend.utils.logger:before_request:68 - [1139bfa8-f092-4a67-bf05-56ddf597a94a] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=im36qv0r
2025-07-30 13:18:18.534 | INFO     | backend.utils.logger:after_request:77 - [1139bfa8-f092-4a67-bf05-56ddf597a94a] Response: 404 Duration: 0.0000s
2025-07-30 13:18:21.848 | INFO     | backend.utils.logger:before_request:68 - [82108758-56ac-4bab-9dd4-8066d53f0858] GET http://localhost:5000/api/statistics/overview
2025-07-30 13:18:21.848 | INFO     | backend.utils.logger:after_request:77 - [82108758-56ac-4bab-9dd4-8066d53f0858] Response: 404 Duration: 0.0000s
2025-07-30 13:18:21.848 | INFO     | backend.utils.logger:before_request:68 - [548d8e24-b0c4-4d53-9ee7-caf9a203a38b] GET http://localhost:5000/api/statistics/recent-executions
2025-07-30 13:18:21.850 | INFO     | backend.utils.logger:before_request:68 - [9fac7cb9-42ba-4b23-8337-60f1ae138b64] GET http://localhost:5000/api/statistics/execution-charts
2025-07-30 13:18:21.850 | INFO     | backend.utils.logger:after_request:77 - [548d8e24-b0c4-4d53-9ee7-caf9a203a38b] Response: 404 Duration: 0.0021s
2025-07-30 13:18:21.850 | INFO     | backend.utils.logger:after_request:77 - [9fac7cb9-42ba-4b23-8337-60f1ae138b64] Response: 404 Duration: 0.0000s
2025-07-30 13:18:24.527 | INFO     | backend.utils.logger:before_request:68 - [2f80b95a-38ea-4075-a346-40727a659ec9] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=im7t9rxl
2025-07-30 13:18:24.529 | INFO     | backend.utils.logger:after_request:77 - [2f80b95a-38ea-4075-a346-40727a659ec9] Response: 404 Duration: 0.0017s
2025-07-30 13:18:30.853 | INFO     | backend.utils.logger:before_request:68 - [e114a137-fbf5-44da-b121-dbbd40a6389d] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=imcgawy8
2025-07-30 13:18:30.853 | INFO     | backend.utils.logger:after_request:77 - [e114a137-fbf5-44da-b121-dbbd40a6389d] Response: 404 Duration: 0.0000s
2025-07-30 13:18:31.101 | INFO     | backend.utils.logger:before_request:68 - [822ec0f5-2a55-4eba-827c-c5dc4418db68] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=imcp9ypd
2025-07-30 13:18:31.102 | INFO     | backend.utils.logger:after_request:77 - [822ec0f5-2a55-4eba-827c-c5dc4418db68] Response: 404 Duration: 0.0009s
2025-07-30 13:18:36.535 | INFO     | backend.utils.logger:before_request:68 - [4b5276ee-5e38-4315-b0ab-30c5bf9fca7c] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=imh2wok1
2025-07-30 13:18:36.535 | INFO     | backend.utils.logger:after_request:77 - [4b5276ee-5e38-4315-b0ab-30c5bf9fca7c] Response: 404 Duration: 0.0000s
2025-07-30 13:18:42.832 | INFO     | backend.utils.logger:before_request:68 - [254de70c-93c4-47c0-b31e-02d14bf07a5a] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=imlpa11u
2025-07-30 13:18:42.835 | INFO     | backend.utils.logger:after_request:77 - [254de70c-93c4-47c0-b31e-02d14bf07a5a] Response: 404 Duration: 0.0030s
2025-07-30 13:18:48.530 | INFO     | backend.utils.logger:before_request:68 - [32141ba3-fce9-4f20-a7f1-8d93487881e9] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=imqc1brd
2025-07-30 13:18:48.530 | INFO     | backend.utils.logger:after_request:77 - [32141ba3-fce9-4f20-a7f1-8d93487881e9] Response: 404 Duration: 0.0000s
2025-07-30 13:18:48.839 | INFO     | backend.utils.logger:before_request:68 - [6b4b2979-787b-44d2-b342-74ae638c8067] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=imqc8bhu
2025-07-30 13:18:48.839 | INFO     | backend.utils.logger:after_request:77 - [6b4b2979-787b-44d2-b342-74ae638c8067] Response: 404 Duration: 0.0000s
2025-07-30 13:18:49.098 | INFO     | backend.utils.logger:before_request:68 - [21afd1a6-be38-4940-940b-9d8e62f12ade] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=imqkt5zy
2025-07-30 13:18:49.099 | INFO     | backend.utils.logger:after_request:77 - [21afd1a6-be38-4940-940b-9d8e62f12ade] Response: 404 Duration: 0.0010s
2025-07-30 13:18:54.544 | INFO     | backend.utils.logger:before_request:68 - [3d1cde3b-ffab-4d9b-8dc1-87ed042c1eac] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=imuywv68
2025-07-30 13:18:54.544 | INFO     | backend.utils.logger:after_request:77 - [3d1cde3b-ffab-4d9b-8dc1-87ed042c1eac] Response: 404 Duration: 0.0009s
2025-07-30 13:18:54.861 | INFO     | backend.utils.logger:before_request:68 - [3af582db-1815-415c-b351-6c960a3e9e23] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=imuzabk2
2025-07-30 13:18:54.862 | INFO     | backend.utils.logger:after_request:77 - [3af582db-1815-415c-b351-6c960a3e9e23] Response: 404 Duration: 0.0010s
2025-07-30 13:18:55.110 | INFO     | backend.utils.logger:before_request:68 - [ad9552f9-5365-40f9-9ea2-14d1e61c6ed6] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=imv81kbk
2025-07-30 13:18:55.111 | INFO     | backend.utils.logger:after_request:77 - [ad9552f9-5365-40f9-9ea2-14d1e61c6ed6] Response: 404 Duration: 0.0010s
2025-07-30 13:18:55.173 | INFO     | backend.utils.logger:before_request:68 - [9796935a-54f0-418f-b586-116c43cd18ef] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=imveyunj
2025-07-30 13:18:55.175 | INFO     | backend.utils.logger:after_request:77 - [9796935a-54f0-418f-b586-116c43cd18ef] Response: 404 Duration: 0.0011s
2025-07-30 13:18:55.427 | INFO     | backend.utils.logger:before_request:68 - [97f3dc88-1e06-43aa-affe-3d41309983a6] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=imvgsai3
2025-07-30 13:18:55.428 | INFO     | backend.utils.logger:after_request:77 - [97f3dc88-1e06-43aa-affe-3d41309983a6] Response: 404 Duration: 0.0014s
2025-07-30 13:19:00.538 | INFO     | backend.utils.logger:before_request:68 - [6a79b978-a341-4eb3-8d93-5bd6cb63d200] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=imzli7xe
2025-07-30 13:19:00.538 | INFO     | backend.utils.logger:after_request:77 - [6a79b978-a341-4eb3-8d93-5bd6cb63d200] Response: 404 Duration: 0.0011s
2025-07-30 13:19:06.833 | INFO     | backend.utils.logger:before_request:68 - [026ea15a-0218-49a6-9d93-cf816c00e30e] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=in47xmx0
2025-07-30 13:19:06.833 | INFO     | backend.utils.logger:after_request:77 - [026ea15a-0218-49a6-9d93-cf816c00e30e] Response: 404 Duration: 0.0000s
2025-07-30 13:19:39.842 | INFO     | backend.utils.logger:before_request:68 - [113a3881-23fe-43f4-9ac4-3502ce1ca1e3] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=intor8qg
2025-07-30 13:19:39.846 | INFO     | backend.utils.logger:after_request:77 - [113a3881-23fe-43f4-9ac4-3502ce1ca1e3] Response: 404 Duration: 0.0035s
2025-07-30 13:19:40.100 | INFO     | backend.utils.logger:before_request:68 - [08f7cbf0-c1fc-45d7-8f41-4ecf1f678a65] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=intxllj5
2025-07-30 13:19:40.100 | INFO     | backend.utils.logger:after_request:77 - [08f7cbf0-c1fc-45d7-8f41-4ecf1f678a65] Response: 404 Duration: 0.0000s
2025-07-30 13:19:45.526 | INFO     | backend.utils.logger:before_request:68 - [c25615ad-1778-4dcb-ac73-4d2de9b33d6b] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=inyba7oh
2025-07-30 13:19:45.526 | INFO     | backend.utils.logger:after_request:77 - [c25615ad-1778-4dcb-ac73-4d2de9b33d6b] Response: 404 Duration: 0.0000s
2025-07-30 13:19:45.954 | INFO     | backend.utils.logger:before_request:68 - [d401dbba-20bf-4b57-9c50-6de04d1c3e9a] GET http://localhost:5000/api/v1/
2025-07-30 13:19:45.954 | INFO     | backend.utils.logger:after_request:77 - [d401dbba-20bf-4b57-9c50-6de04d1c3e9a] Response: 200 Duration: 0.0000s
2025-07-30 13:19:50.277 | INFO     | backend.utils.logger:before_request:68 - [5c6036dd-edab-45d2-a5b7-369b8f3e45a9] GET http://localhost:5000/api/v1/health
2025-07-30 13:19:50.277 | INFO     | backend.utils.logger:after_request:77 - [5c6036dd-edab-45d2-a5b7-369b8f3e45a9] Response: 200 Duration: 0.0000s
2025-07-30 13:19:51.843 | INFO     | backend.utils.logger:before_request:68 - [21217519-6363-4897-9691-26363ae16725] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=io2y6zxs
2025-07-30 13:19:51.843 | INFO     | backend.utils.logger:after_request:77 - [21217519-6363-4897-9691-26363ae16725] Response: 404 Duration: 0.0000s
2025-07-30 13:19:54.541 | INFO     | backend.utils.logger:before_request:68 - [824148f7-744b-4611-ac35-181dbbc5f276] GET http://localhost:5000/api/v1/tasks
2025-07-30 13:19:54.542 | INFO     | backend.utils.logger:after_request:77 - [824148f7-744b-4611-ac35-181dbbc5f276] Response: 308 Duration: 0.0010s
2025-07-30 13:19:57.531 | INFO     | backend.utils.logger:before_request:68 - [3de9edbb-50e6-4eef-84c7-bb3019e1f533] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=io7kntum
2025-07-30 13:19:57.531 | INFO     | backend.utils.logger:after_request:77 - [3de9edbb-50e6-4eef-84c7-bb3019e1f533] Response: 404 Duration: 0.0000s
2025-07-30 13:19:57.840 | INFO     | backend.utils.logger:before_request:68 - [2e239ed8-16a9-4059-8c5f-72a079e968a2] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=io7kwlbp
2025-07-30 13:19:57.841 | INFO     | backend.utils.logger:after_request:77 - [2e239ed8-16a9-4059-8c5f-72a079e968a2] Response: 404 Duration: 0.0011s
2025-07-30 13:20:03.528 | INFO     | backend.utils.logger:before_request:68 - [4bb2dc57-ee96-4002-90ba-f2ab35c75881] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ioc7bk8b
2025-07-30 13:20:03.528 | INFO     | backend.utils.logger:after_request:77 - [4bb2dc57-ee96-4002-90ba-f2ab35c75881] Response: 404 Duration: 0.0000s
2025-07-30 13:20:03.842 | INFO     | backend.utils.logger:before_request:68 - [b988a5b0-b565-47af-8cf9-0968bc9e58da] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ioc7jpgj
2025-07-30 13:20:03.842 | INFO     | backend.utils.logger:after_request:77 - [b988a5b0-b565-47af-8cf9-0968bc9e58da] Response: 404 Duration: 0.0000s
2025-07-30 13:20:09.532 | INFO     | backend.utils.logger:before_request:68 - [63cea2b7-397d-4092-b790-a4828310fba0] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iogu4wkr
2025-07-30 13:20:09.532 | INFO     | backend.utils.logger:after_request:77 - [63cea2b7-397d-4092-b790-a4828310fba0] Response: 404 Duration: 0.0000s
2025-07-30 13:20:13.010 | INFO     | backend.utils.logger:before_request:68 - [b82421ba-034d-4744-be4a-52d3d2fab869] GET http://localhost:5000/api/v1/tasks
2025-07-30 13:20:13.012 | INFO     | backend.utils.logger:after_request:77 - [b82421ba-034d-4744-be4a-52d3d2fab869] Response: 308 Duration: 0.0020s
2025-07-30 13:20:15.833 | INFO     | backend.utils.logger:before_request:68 - [23b5b49b-c4da-4ca8-b240-c7faf2a5be5c] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iolgnghk
2025-07-30 13:20:15.833 | INFO     | backend.utils.logger:after_request:77 - [23b5b49b-c4da-4ca8-b240-c7faf2a5be5c] Response: 404 Duration: 0.0000s
2025-07-30 13:20:16.087 | INFO     | backend.utils.logger:before_request:68 - [c2824d1b-16c0-4543-ae31-921ea8556510] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iolpb1fu
2025-07-30 13:20:16.087 | INFO     | backend.utils.logger:after_request:77 - [c2824d1b-16c0-4543-ae31-921ea8556510] Response: 404 Duration: 0.0000s
2025-07-30 13:20:21.529 | INFO     | backend.utils.logger:before_request:68 - [c6414e8a-05b7-4c7e-bb6f-1b0da02452d5] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ioq3boph
2025-07-30 13:20:21.531 | INFO     | backend.utils.logger:after_request:77 - [c6414e8a-05b7-4c7e-bb6f-1b0da02452d5] Response: 404 Duration: 0.0015s
2025-07-30 13:20:21.837 | INFO     | backend.utils.logger:before_request:68 - [0478d33b-cb61-40e5-8c68-4cec5d6df6aa] GET http://localhost:5000/api/statistics/overview
2025-07-30 13:20:21.837 | INFO     | backend.utils.logger:before_request:68 - [1315ca68-7a49-4270-b012-955c96552447] GET http://localhost:5000/api/statistics/execution-charts
2025-07-30 13:20:21.837 | INFO     | backend.utils.logger:before_request:68 - [3d8e5eaa-fc0b-457c-aeaf-e72af764f1e4] GET http://localhost:5000/api/statistics/recent-executions
2025-07-30 13:20:21.837 | INFO     | backend.utils.logger:after_request:77 - [0478d33b-cb61-40e5-8c68-4cec5d6df6aa] Response: 404 Duration: 0.0000s
2025-07-30 13:20:21.837 | INFO     | backend.utils.logger:after_request:77 - [1315ca68-7a49-4270-b012-955c96552447] Response: 404 Duration: 0.0000s
2025-07-30 13:20:21.837 | INFO     | backend.utils.logger:after_request:77 - [3d8e5eaa-fc0b-457c-aeaf-e72af764f1e4] Response: 404 Duration: 0.0000s
2025-07-30 13:20:59.851 | INFO     | backend.utils.logger:before_request:68 - [382aa151-ec0b-417f-ab89-d796aa7f5179] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ipjew8u4
2025-07-30 13:20:59.851 | INFO     | backend.utils.logger:after_request:77 - [382aa151-ec0b-417f-ab89-d796aa7f5179] Response: 404 Duration: 0.0000s
2025-07-30 13:21:05.528 | INFO     | backend.utils.logger:before_request:68 - [1d54c6f3-7343-40f5-b196-17362256041e] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ipo1jhar
2025-07-30 13:21:05.530 | INFO     | backend.utils.logger:after_request:77 - [1d54c6f3-7343-40f5-b196-17362256041e] Response: 404 Duration: 0.0015s
2025-07-30 13:21:05.658 | INFO     | backend.utils.logger:before_request:68 - [9e3298c7-1206-4d82-a620-ba68dbfe34cd] GET http://localhost:5000/api/v1/tasks
2025-07-30 13:21:05.658 | INFO     | backend.utils.logger:after_request:77 - [9e3298c7-1206-4d82-a620-ba68dbfe34cd] Response: 200 Duration: 0.0003s
2025-07-30 13:21:10.995 | INFO     | backend.utils.logger:before_request:68 - [2336f81d-48bc-41e2-9753-3f410a8129d1] POST http://localhost:5000/api/v1/tasks
2025-07-30 13:21:10.995 | INFO     | backend.utils.logger:after_request:77 - [2336f81d-48bc-41e2-9753-3f410a8129d1] Response: 201 Duration: 0.0000s
2025-07-30 13:21:11.836 | INFO     | backend.utils.logger:before_request:68 - [e99a54c1-871d-47ba-939f-3147858d2d2e] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ipso5l4z
2025-07-30 13:21:11.837 | INFO     | backend.utils.logger:after_request:77 - [e99a54c1-871d-47ba-939f-3147858d2d2e] Response: 404 Duration: 0.0020s
2025-07-30 13:21:15.356 | INFO     | backend.utils.logger:before_request:68 - [5446fc1e-c72b-4a43-81e8-3e04afbc363e] GET http://localhost:5000/api/v1/tasks/1
2025-07-30 13:21:15.357 | INFO     | backend.utils.logger:after_request:77 - [5446fc1e-c72b-4a43-81e8-3e04afbc363e] Response: 200 Duration: 0.0010s
2025-07-30 13:21:17.529 | INFO     | backend.utils.logger:before_request:68 - [b731c7ff-19a0-4fa9-a164-9fa6edbfa77e] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ipxaxcx8
2025-07-30 13:21:17.534 | INFO     | backend.utils.logger:after_request:77 - [b731c7ff-19a0-4fa9-a164-9fa6edbfa77e] Response: 404 Duration: 0.0043s
2025-07-30 13:21:17.847 | INFO     | backend.utils.logger:before_request:68 - [e3665613-02eb-4743-95b0-801272433965] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ipxb4zg8
2025-07-30 13:21:17.847 | INFO     | backend.utils.logger:after_request:77 - [e3665613-02eb-4743-95b0-801272433965] Response: 404 Duration: 0.0000s
2025-07-30 13:21:20.166 | INFO     | backend.utils.logger:before_request:68 - [e6113209-40c8-4190-8a49-5ef76ea0be3d] PUT http://localhost:5000/api/v1/tasks/1
2025-07-30 13:21:20.166 | INFO     | backend.utils.logger:after_request:77 - [e6113209-40c8-4190-8a49-5ef76ea0be3d] Response: 200 Duration: 0.0000s
2025-07-30 13:21:21.535 | INFO     | backend.utils.logger:before_request:68 - [2ba94a43-cb69-47b0-85b5-f601266a1ac3] GET http://localhost:5000/api/statistics/overview
2025-07-30 13:21:21.536 | INFO     | backend.utils.logger:after_request:77 - [2ba94a43-cb69-47b0-85b5-f601266a1ac3] Response: 404 Duration: 0.0013s
2025-07-30 13:21:21.845 | INFO     | backend.utils.logger:before_request:68 - [705f7e1e-7d43-4302-95bb-38df9b4a9519] GET http://localhost:5000/api/statistics/execution-charts
2025-07-30 13:21:21.845 | INFO     | backend.utils.logger:before_request:68 - [bb098fa7-58cd-4ca6-899c-6a1c287a6f63] GET http://localhost:5000/api/statistics/recent-executions
2025-07-30 13:21:21.848 | INFO     | backend.utils.logger:after_request:77 - [705f7e1e-7d43-4302-95bb-38df9b4a9519] Response: 404 Duration: 0.0030s
2025-07-30 13:21:21.848 | INFO     | backend.utils.logger:after_request:77 - [bb098fa7-58cd-4ca6-899c-6a1c287a6f63] Response: 404 Duration: 0.0030s
2025-07-30 13:21:23.849 | INFO     | backend.utils.logger:before_request:68 - [fe5198fa-a726-4763-992e-73c35f5e74b2] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iq1xsacn
2025-07-30 13:21:23.849 | INFO     | backend.utils.logger:after_request:77 - [fe5198fa-a726-4763-992e-73c35f5e74b2] Response: 404 Duration: 0.0000s
2025-07-30 13:21:24.696 | INFO     | backend.utils.logger:before_request:68 - [dda95c9b-1781-4322-8b84-b224bac6ac78] DELETE http://localhost:5000/api/v1/tasks/1
2025-07-30 13:21:24.696 | INFO     | backend.utils.logger:after_request:77 - [dda95c9b-1781-4322-8b84-b224bac6ac78] Response: 200 Duration: 0.0000s
2025-07-30 13:21:29.539 | INFO     | backend.utils.logger:before_request:68 - [758935fe-9978-4dc4-9bd0-b3bfd4bf2fd5] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iq6kfm70
2025-07-30 13:21:29.540 | INFO     | backend.utils.logger:after_request:77 - [758935fe-9978-4dc4-9bd0-b3bfd4bf2fd5] Response: 404 Duration: 0.0010s
2025-07-30 13:21:35.839 | INFO     | backend.utils.logger:before_request:68 - [7dd941a1-9330-4a98-b8dd-a54a8ff6c778] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iqb6xhtb
2025-07-30 13:21:35.840 | INFO     | backend.utils.logger:after_request:77 - [7dd941a1-9330-4a98-b8dd-a54a8ff6c778] Response: 404 Duration: 0.0009s
2025-07-30 13:21:41.528 | INFO     | backend.utils.logger:before_request:68 - [742b8bf7-69ce-4d1c-9229-45f429c6b70f] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iqftkfg5
2025-07-30 13:21:41.528 | INFO     | backend.utils.logger:after_request:77 - [742b8bf7-69ce-4d1c-9229-45f429c6b70f] Response: 404 Duration: 0.0000s
2025-07-30 13:21:41.838 | INFO     | backend.utils.logger:before_request:68 - [8991261d-75fc-4a33-8aa6-f9bc53126b24] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iqftqpwn
2025-07-30 13:21:41.838 | INFO     | backend.utils.logger:after_request:77 - [8991261d-75fc-4a33-8aa6-f9bc53126b24] Response: 404 Duration: 0.0000s
2025-07-30 13:21:42.088 | INFO     | backend.utils.logger:before_request:68 - [a0b9bae6-8516-4e8e-8f7e-cc9440c8b001] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iqg2ak1k
2025-07-30 13:21:42.094 | INFO     | backend.utils.logger:after_request:77 - [a0b9bae6-8516-4e8e-8f7e-cc9440c8b001] Response: 404 Duration: 0.0059s
2025-07-30 13:21:47.531 | INFO     | backend.utils.logger:before_request:68 - [18e70129-4d07-4383-a15c-91dd6366dc23] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iqkg8zqk
2025-07-30 13:21:47.531 | INFO     | backend.utils.logger:after_request:77 - [18e70129-4d07-4383-a15c-91dd6366dc23] Response: 404 Duration: 0.0000s
2025-07-30 13:21:53.851 | INFO     | backend.utils.logger:before_request:68 - [747e216e-5916-4073-96fd-17640d7bf4d3] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iqp33oj1
2025-07-30 13:21:53.854 | INFO     | backend.utils.logger:after_request:77 - [747e216e-5916-4073-96fd-17640d7bf4d3] Response: 404 Duration: 0.0036s
2025-07-30 13:21:59.535 | INFO     | backend.utils.logger:before_request:68 - [9ef310e3-8c63-42e1-b842-c82075e62bb4] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iqtpm1mg
2025-07-30 13:21:59.536 | INFO     | backend.utils.logger:after_request:77 - [9ef310e3-8c63-42e1-b842-c82075e62bb4] Response: 404 Duration: 0.0010s
2025-07-30 13:22:05.840 | INFO     | backend.utils.logger:before_request:68 - [0bb42ce8-3ff6-4a93-a01a-c305b057d2fc] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iqycahca
2025-07-30 13:22:05.840 | INFO     | backend.utils.logger:after_request:77 - [0bb42ce8-3ff6-4a93-a01a-c305b057d2fc] Response: 404 Duration: 0.0000s
2025-07-30 13:22:06.091 | INFO     | backend.utils.logger:before_request:68 - [24f3f6c6-aa39-441b-9573-d6287dd38b02] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iqyl0qyf
2025-07-30 13:22:06.091 | INFO     | backend.utils.logger:after_request:77 - [24f3f6c6-aa39-441b-9573-d6287dd38b02] Response: 404 Duration: 0.0000s
2025-07-30 13:22:06.151 | INFO     | backend.utils.logger:before_request:68 - [4f0f3c26-1eed-493a-ac49-404c2cf224b7] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iqys3k2w
2025-07-30 13:22:06.153 | INFO     | backend.utils.logger:after_request:77 - [4f0f3c26-1eed-493a-ac49-404c2cf224b7] Response: 404 Duration: 0.0023s
2025-07-30 13:22:14.520 | INFO     | backend.utils.logger:before_request:68 - [2b69dcfb-7072-4cd6-967b-8af9032b01df] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ir59pvjn
2025-07-30 13:22:14.520 | INFO     | backend.utils.logger:after_request:77 - [2b69dcfb-7072-4cd6-967b-8af9032b01df] Response: 404 Duration: 0.0000s
2025-07-30 13:22:20.837 | INFO     | backend.utils.logger:before_request:68 - [cbf87c6d-1d1b-4546-93af-b1305b401a5b] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ir9wyf2z
2025-07-30 13:22:20.839 | INFO     | backend.utils.logger:after_request:77 - [cbf87c6d-1d1b-4546-93af-b1305b401a5b] Response: 404 Duration: 0.0022s
2025-07-30 13:22:21.536 | INFO     | backend.utils.logger:before_request:68 - [c6ce98b4-eb5c-4c4f-bc57-d5a115c666c4] GET http://localhost:5000/api/statistics/overview
2025-07-30 13:22:21.537 | INFO     | backend.utils.logger:after_request:77 - [c6ce98b4-eb5c-4c4f-bc57-d5a115c666c4] Response: 404 Duration: 0.0009s
2025-07-30 13:22:21.837 | INFO     | backend.utils.logger:before_request:68 - [9747bbbd-0674-4f05-973d-d3af4daced54] GET http://localhost:5000/api/statistics/execution-charts
2025-07-30 13:22:21.837 | INFO     | backend.utils.logger:before_request:68 - [088424af-292b-4576-b04e-5888bcd9fc33] GET http://localhost:5000/api/statistics/recent-executions
2025-07-30 13:22:21.837 | INFO     | backend.utils.logger:after_request:77 - [9747bbbd-0674-4f05-973d-d3af4daced54] Response: 404 Duration: 0.0000s
2025-07-30 13:22:21.837 | INFO     | backend.utils.logger:after_request:77 - [088424af-292b-4576-b04e-5888bcd9fc33] Response: 404 Duration: 0.0000s
2025-07-30 13:22:26.831 | INFO     | backend.utils.logger:before_request:68 - [0d86d246-ac2c-4b48-9c6d-6cc6fac23065] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=irejjz5d
2025-07-30 13:22:26.832 | INFO     | backend.utils.logger:after_request:77 - [0d86d246-ac2c-4b48-9c6d-6cc6fac23065] Response: 404 Duration: 0.0010s
2025-07-30 13:22:27.099 | INFO     | backend.utils.logger:before_request:68 - [40d69cfb-3189-4472-a748-d556c406291b] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ires4u5u
2025-07-30 13:22:27.100 | INFO     | backend.utils.logger:after_request:77 - [40d69cfb-3189-4472-a748-d556c406291b] Response: 404 Duration: 0.0010s
2025-07-30 13:22:27.155 | INFO     | backend.utils.logger:before_request:68 - [c5a444db-16ca-4ca3-b7ac-3a011b445fe8] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=irezkea2
2025-07-30 13:22:27.156 | INFO     | backend.utils.logger:after_request:77 - [c5a444db-16ca-4ca3-b7ac-3a011b445fe8] Response: 404 Duration: 0.0010s
2025-07-30 13:22:27.413 | INFO     | backend.utils.logger:before_request:68 - [b1885a97-6242-4f36-a518-20f14936823c] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=irf1405g
2025-07-30 13:22:27.414 | INFO     | backend.utils.logger:after_request:77 - [b1885a97-6242-4f36-a518-20f14936823c] Response: 404 Duration: 0.0010s
2025-07-30 13:22:32.531 | INFO     | backend.utils.logger:before_request:68 - [775adb74-b599-4436-84a4-a07840cccd39] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=irj6a38f
2025-07-30 13:22:32.531 | INFO     | backend.utils.logger:after_request:77 - [775adb74-b599-4436-84a4-a07840cccd39] Response: 404 Duration: 0.0000s
2025-07-30 13:22:32.845 | INFO     | backend.utils.logger:before_request:68 - [1c366508-4568-4b3e-ba39-077cf837d8e7] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=irj6fim9
2025-07-30 13:22:32.845 | INFO     | backend.utils.logger:after_request:77 - [1c366508-4568-4b3e-ba39-077cf837d8e7] Response: 404 Duration: 0.0000s
2025-07-30 13:22:33.097 | INFO     | backend.utils.logger:before_request:68 - [5d8b9e7d-a7c4-4e6a-8f2d-eba2a8041ce2] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=irjf6wji
2025-07-30 13:22:33.097 | INFO     | backend.utils.logger:after_request:77 - [5d8b9e7d-a7c4-4e6a-8f2d-eba2a8041ce2] Response: 404 Duration: 0.0000s
2025-07-30 13:22:33.157 | INFO     | backend.utils.logger:before_request:68 - [d371bbdc-4870-498e-b862-a4e49f6b6094] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=irjm66je
2025-07-30 13:22:33.157 | INFO     | backend.utils.logger:after_request:77 - [d371bbdc-4870-498e-b862-a4e49f6b6094] Response: 404 Duration: 0.0000s
2025-07-30 13:22:33.410 | INFO     | backend.utils.logger:before_request:68 - [a0a58ab2-1d75-4a5a-a4ff-e12326912c4b] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=irjnw5vd
2025-07-30 13:22:33.410 | INFO     | backend.utils.logger:after_request:77 - [a0a58ab2-1d75-4a5a-a4ff-e12326912c4b] Response: 404 Duration: 0.0000s
2025-07-30 13:22:33.471 | INFO     | backend.utils.logger:before_request:68 - [ba778e56-b962-41af-aa57-b1e1c88fb2e3] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=irjuxsy2
2025-07-30 13:22:33.471 | INFO     | backend.utils.logger:after_request:77 - [ba778e56-b962-41af-aa57-b1e1c88fb2e3] Response: 404 Duration: 0.0000s
2025-07-30 13:22:33.724 | INFO     | backend.utils.logger:before_request:68 - [4f44bafb-6951-4178-854d-b752d8f2a43a] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=irjwlwrm
2025-07-30 13:22:33.724 | INFO     | backend.utils.logger:after_request:77 - [4f44bafb-6951-4178-854d-b752d8f2a43a] Response: 404 Duration: 0.0000s
2025-07-30 13:22:33.779 | INFO     | backend.utils.logger:before_request:68 - [ecb4bb11-2bb2-49d2-85a1-46b56b995b58] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=irk3n1pn
2025-07-30 13:22:33.779 | INFO     | backend.utils.logger:after_request:77 - [ecb4bb11-2bb2-49d2-85a1-46b56b995b58] Response: 404 Duration: 0.0000s
2025-07-30 13:22:34.040 | INFO     | backend.utils.logger:before_request:68 - [61d100e3-22e7-4255-bc1c-68aa57fd6d8e] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=irk54v43
2025-07-30 13:22:34.040 | INFO     | backend.utils.logger:after_request:77 - [61d100e3-22e7-4255-bc1c-68aa57fd6d8e] Response: 404 Duration: 0.0000s
2025-07-30 13:22:39.532 | INFO     | backend.utils.logger:before_request:68 - [0292306f-8fee-4774-9c02-91a817fc7476] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=irokr9ka
2025-07-30 13:22:39.532 | INFO     | backend.utils.logger:after_request:77 - [0292306f-8fee-4774-9c02-91a817fc7476] Response: 404 Duration: 0.0000s
2025-07-30 13:22:45.836 | INFO     | backend.utils.logger:before_request:68 - [45a7933e-3bf3-4ec8-bfd2-570b1def9ff8] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=irt7cj6m
2025-07-30 13:22:45.839 | INFO     | backend.utils.logger:after_request:77 - [45a7933e-3bf3-4ec8-bfd2-570b1def9ff8] Response: 404 Duration: 0.0033s
2025-07-30 13:22:51.529 | INFO     | backend.utils.logger:before_request:68 - [cbb3ef16-a364-4033-8beb-3907a17b1632] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=irxtz2zk
2025-07-30 13:22:51.529 | INFO     | backend.utils.logger:after_request:77 - [cbb3ef16-a364-4033-8beb-3907a17b1632] Response: 404 Duration: 0.0000s
2025-07-30 13:22:57.837 | INFO     | backend.utils.logger:before_request:68 - [1144c7e1-6349-40fa-9b65-fab6eba860a0] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=is2grjqs
2025-07-30 13:22:57.838 | INFO     | backend.utils.logger:after_request:77 - [1144c7e1-6349-40fa-9b65-fab6eba860a0] Response: 404 Duration: 0.0010s
2025-07-30 13:23:03.535 | INFO     | backend.utils.logger:before_request:68 - [2050fb18-2a9a-476a-a904-48d878445cca] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=is73ddyi
2025-07-30 13:23:03.535 | INFO     | backend.utils.logger:after_request:77 - [2050fb18-2a9a-476a-a904-48d878445cca] Response: 404 Duration: 0.0009s
2025-07-30 13:23:03.853 | INFO     | backend.utils.logger:before_request:68 - [487949f9-1803-4bad-a92b-4dd3b830d0cf] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=is73nbs8
2025-07-30 13:23:03.855 | INFO     | backend.utils.logger:after_request:77 - [487949f9-1803-4bad-a92b-4dd3b830d0cf] Response: 404 Duration: 0.0020s
2025-07-30 13:23:04.103 | INFO     | backend.utils.logger:before_request:68 - [c2400393-c6a8-4d87-a123-a2bee1da1806] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=is7cjpnq
2025-07-30 13:23:04.104 | INFO     | backend.utils.logger:after_request:77 - [c2400393-c6a8-4d87-a123-a2bee1da1806] Response: 404 Duration: 0.0010s
2025-07-30 13:23:04.163 | INFO     | backend.utils.logger:before_request:68 - [5d42471c-3c19-4aa8-ac51-ddbd4c58b3a7] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=is7jgzlf
2025-07-30 13:23:04.164 | INFO     | backend.utils.logger:after_request:77 - [5d42471c-3c19-4aa8-ac51-ddbd4c58b3a7] Response: 404 Duration: 0.0010s
2025-07-30 13:23:04.419 | INFO     | backend.utils.logger:before_request:68 - [7e43afd0-acfc-4d8f-92cb-0b837fa46ddb] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=is7l3zlr
2025-07-30 13:23:04.420 | INFO     | backend.utils.logger:after_request:77 - [7e43afd0-acfc-4d8f-92cb-0b837fa46ddb] Response: 404 Duration: 0.0015s
2025-07-30 13:23:09.535 | INFO     | backend.utils.logger:before_request:68 - [c5448f21-650a-4c87-a04c-2bd70e62abee] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=isbq3iav
2025-07-30 13:23:09.536 | INFO     | backend.utils.logger:after_request:77 - [c5448f21-650a-4c87-a04c-2bd70e62abee] Response: 404 Duration: 0.0010s
2025-07-30 13:23:09.848 | INFO     | backend.utils.logger:before_request:68 - [d9054b93-a40d-43d4-8cce-bec20cf2954e] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=isbqc3fq
2025-07-30 13:23:09.848 | INFO     | backend.utils.logger:after_request:77 - [d9054b93-a40d-43d4-8cce-bec20cf2954e] Response: 404 Duration: 0.0000s
2025-07-30 13:23:10.104 | INFO     | backend.utils.logger:before_request:68 - [b4d29d75-8dbb-4ec8-b12a-558df6baf81f] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=isbz3sum
2025-07-30 13:23:10.104 | INFO     | backend.utils.logger:after_request:77 - [b4d29d75-8dbb-4ec8-b12a-558df6baf81f] Response: 404 Duration: 0.0000s
2025-07-30 13:23:15.536 | INFO     | backend.utils.logger:before_request:68 - [4d1ca863-750b-4a3c-ac61-af46fbfece95] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=isgctn0k
2025-07-30 13:23:15.536 | INFO     | backend.utils.logger:after_request:77 - [4d1ca863-750b-4a3c-ac61-af46fbfece95] Response: 404 Duration: 0.0000s
2025-07-30 13:23:21.837 | INFO     | backend.utils.logger:before_request:68 - [c0824341-3637-414a-a80c-8b0e93920554] GET http://localhost:5000/api/statistics/overview
2025-07-30 13:23:21.841 | INFO     | backend.utils.logger:before_request:68 - [229dc189-d052-44c9-9c7a-6fa619b8da62] GET http://localhost:5000/api/statistics/execution-charts
2025-07-30 13:23:21.841 | INFO     | backend.utils.logger:before_request:68 - [088105bf-8b47-441d-9295-0d1368b390f9] GET http://localhost:5000/api/statistics/recent-executions
2025-07-30 13:23:21.841 | INFO     | backend.utils.logger:before_request:68 - [233f947b-7ec9-42be-bd89-8bcaeb939bb2] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=iskzcqhb
2025-07-30 13:23:21.841 | INFO     | backend.utils.logger:after_request:77 - [c0824341-3637-414a-a80c-8b0e93920554] Response: 404 Duration: 0.0048s
2025-07-30 13:23:21.841 | INFO     | backend.utils.logger:after_request:77 - [229dc189-d052-44c9-9c7a-6fa619b8da62] Response: 404 Duration: 0.0011s
2025-07-30 13:23:21.843 | INFO     | backend.utils.logger:after_request:77 - [088105bf-8b47-441d-9295-0d1368b390f9] Response: 404 Duration: 0.0019s
2025-07-30 13:23:21.843 | INFO     | backend.utils.logger:after_request:77 - [233f947b-7ec9-42be-bd89-8bcaeb939bb2] Response: 404 Duration: 0.0012s
2025-07-30 13:23:22.097 | INFO     | backend.utils.logger:before_request:68 - [5a0cea11-0ca1-4ff9-8de2-61287ff299e9] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=isl9h26d
2025-07-30 13:23:22.098 | INFO     | backend.utils.logger:after_request:77 - [5a0cea11-0ca1-4ff9-8de2-61287ff299e9] Response: 404 Duration: 0.0010s
2025-07-30 13:23:27.528 | INFO     | backend.utils.logger:before_request:68 - [95eae07f-0601-463c-b7c5-7227657e91d9] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ispm1yqo
2025-07-30 13:23:27.528 | INFO     | backend.utils.logger:after_request:77 - [95eae07f-0601-463c-b7c5-7227657e91d9] Response: 404 Duration: 0.0000s
2025-07-30 13:23:27.847 | INFO     | backend.utils.logger:before_request:68 - [a243c2d0-0531-485d-a384-82b9b521ce1a] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ispm7x6m
2025-07-30 13:23:27.848 | INFO     | backend.utils.logger:after_request:77 - [a243c2d0-0531-485d-a384-82b9b521ce1a] Response: 404 Duration: 0.0023s
2025-07-30 13:23:33.529 | INFO     | backend.utils.logger:before_request:68 - [b8bb62ec-4601-41cb-86a4-82fc33792c1b] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=isu8orvm
2025-07-30 13:23:33.529 | INFO     | backend.utils.logger:after_request:77 - [b8bb62ec-4601-41cb-86a4-82fc33792c1b] Response: 404 Duration: 0.0000s
2025-07-30 13:23:39.845 | INFO     | backend.utils.logger:before_request:68 - [93707542-05f9-48bf-86cd-badd96ec436d] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=isyvigqk
2025-07-30 13:23:39.845 | INFO     | backend.utils.logger:after_request:77 - [93707542-05f9-48bf-86cd-badd96ec436d] Response: 404 Duration: 0.0000s
2025-07-30 13:23:45.534 | INFO     | backend.utils.logger:before_request:68 - [07b231e5-e8d5-4e0e-93cd-ea974f54fc15] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=it3i2nb5
2025-07-30 13:23:45.535 | INFO     | backend.utils.logger:after_request:77 - [07b231e5-e8d5-4e0e-93cd-ea974f54fc15] Response: 404 Duration: 0.0007s
2025-07-30 13:23:45.849 | INFO     | backend.utils.logger:before_request:68 - [3235994b-a556-4666-9e05-251e8ce4c41e] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=it3iahqc
2025-07-30 13:23:45.849 | INFO     | backend.utils.logger:after_request:77 - [3235994b-a556-4666-9e05-251e8ce4c41e] Response: 404 Duration: 0.0000s
2025-07-30 13:23:46.099 | INFO     | backend.utils.logger:before_request:68 - [fd27ef56-cee5-4f7a-9cdd-d01265673310] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=it3r2d3i
2025-07-30 13:23:46.101 | INFO     | backend.utils.logger:after_request:77 - [fd27ef56-cee5-4f7a-9cdd-d01265673310] Response: 404 Duration: 0.0020s
2025-07-30 13:23:46.158 | INFO     | backend.utils.logger:before_request:68 - [03e5eeac-154b-4bc2-b40d-815675b7397c] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=it3y1uu6
2025-07-30 13:23:46.158 | INFO     | backend.utils.logger:after_request:77 - [03e5eeac-154b-4bc2-b40d-815675b7397c] Response: 404 Duration: 0.0000s
2025-07-30 13:23:46.414 | INFO     | backend.utils.logger:before_request:68 - [622cd503-c453-4fc5-a1a4-8eeb78591844] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=it3zpxho
2025-07-30 13:23:46.414 | INFO     | backend.utils.logger:after_request:77 - [622cd503-c453-4fc5-a1a4-8eeb78591844] Response: 404 Duration: 0.0000s
2025-07-30 13:23:52.097 | INFO     | backend.utils.logger:before_request:68 - [f5d4a66d-5023-43bd-a5e8-e6675297659d] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=it84udv2
2025-07-30 13:23:52.097 | INFO     | backend.utils.logger:after_request:77 - [f5d4a66d-5023-43bd-a5e8-e6675297659d] Response: 404 Duration: 0.0000s
2025-07-30 13:23:57.534 | INFO     | backend.utils.logger:before_request:68 - [90137356-f856-4144-b2d0-fe41bcfe41ae] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=itcrifby
2025-07-30 13:23:57.534 | INFO     | backend.utils.logger:after_request:77 - [90137356-f856-4144-b2d0-fe41bcfe41ae] Response: 404 Duration: 0.0000s
2025-07-30 13:23:57.844 | INFO     | backend.utils.logger:before_request:68 - [108e94df-2fc0-4ce0-aef4-1d27fd1563b5] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=itcrowzo
2025-07-30 13:23:57.847 | INFO     | backend.utils.logger:after_request:77 - [108e94df-2fc0-4ce0-aef4-1d27fd1563b5] Response: 404 Duration: 0.0025s
2025-07-30 13:24:03.526 | INFO     | backend.utils.logger:before_request:68 - [b2520d95-c82a-4f59-9641-19c04766fc14] GET http://localhost:5000/socket.io/?EIO=4&transport=polling&t=ithdx7gn
2025-07-30 13:24:03.527 | INFO     | backend.utils.logger:after_request:77 - [b2520d95-c82a-4f59-9641-19c04766fc14] Response: 404 Duration: 0.0010s
